<template>
  <view class="toolbox-container">
    <view class="toolbox-header">
      <text class="header-title">百宝箱</text>
      <text class="header-subtitle">为英语教学量身打造的智能工具集</text>
    </view>

    <view class="tool-grid">
      <view class="tool-card" @click="navigateTo('quiz-generator')">
        <image class="tool-icon" src="/static/icons/quiz-generator.png" />
        <text class="tool-name">智能出题官</text>
        <text class="tool-description">阅读/完形填空/词汇一键出题</text>
      </view>
      <view class="tool-card" @click="navigateTo('speaking-practice')">
        <image class="tool-icon" src="/static/icons/speaking-practice.png" />
        <text class="tool-name">口语练习馆</text>
        <text class="tool-description">AI发音评测，生成对话话题</text>
      </view>
      <view class="tool-card" @click="navigateTo('reading-assistant')">
        <image class="tool-icon" src="/static/icons/reading-assistant.png" />
        <text class="tool-name">阅读小助手</text>
        <text class="tool-description">文章分级，提取重点词汇</text>
      </view>
      <view class="tool-card disabled">
        <image class="tool-icon" src="/static/icons/classroom-tools.png" />
        <text class="tool-name">课堂小工具</text>
        <text class="tool-description">随机点名/计时器（敬请期待）</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "ToolboxPage",
  methods: {
    navigateTo(page) {
      uni.navigateTo({
        url: `/pages/toolbox/${page}/index`
      });
    }
  }
};
</script>

<style scoped>
.toolbox-container {
  padding: 40rpx;
  background-color: #f5f6fa;
  min-height: 100vh;
}

.toolbox-header {
  margin-bottom: 40rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #2c3e50;
}

.header-subtitle {
  font-size: 28rpx;
  color: #888;
  margin-top: 10rpx;
}

.tool-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.tool-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
}

.tool-card:active {
  transform: scale(0.98);
}

.tool-card.disabled {
  background-color: #f2f2f2;
  opacity: 0.6;
}

.tool-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
}

.tool-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.tool-description {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
</style>
