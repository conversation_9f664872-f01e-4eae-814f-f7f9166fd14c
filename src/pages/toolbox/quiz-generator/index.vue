<template>
  <view class="quiz-generator-container">
    <CustomNavBar title="智能出TCP官" />
    <view class="page-content">
      <view class="textarea-container">
        <textarea
          v-model="text"
          class="text-input"
          placeholder="请输入或粘贴文本内容..."
          maxlength="-1"
          auto-height
        ></textarea>
      </view>

      <view class="options-container">
        <view class="option">
          <text class="option-label">题目数量</text>
          <input type="number" v-model.number="questionCount" class="option-input" />
        </view>
        <view class="option">
          <text class="option-label">题目类型</text>
          <picker @change="bindPickerChange" :value="questionTypeIndex" :range="questionTypes">
            <view class="picker">
              {{questionTypes[questionTypeIndex]}}
            </view>
          </picker>
        </view>
      </view>

      <button class="generate-button" @click="generateQuiz">生成题目</button>

      <view v-if="generatedQuestions.length > 0" class="results-container">
        <text class="results-title">生成的题目</text>
        <view class="question-list">
          <view v-for="(item, index) in generatedQuestions" :key="index" class="question-item">
            <text class="question-text">{{ index + 1 }}. {{ item.question }}</text>
            <view class="choices">
              <text v-for="(choice, cIndex) in item.choices" :key="cIndex" class="choice">
                {{ String.fromCharCode(65 + cIndex) }}. {{ choice }}
              </text>
            </view>
            <text class="answer">正确答案: {{ item.answer }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import CustomNavBar from "@/components/CustomNavBar.vue";

export default {
  name: "QuizGenerator",
  components: {
    CustomNavBar
  },
  data() {
    return {
      text: "",
      questionCount: 5,
      questionTypeIndex: 0,
      questionTypes: ["单项选择", "完形填空", "词汇考察"],
      generatedQuestions: []
    };
  },
  methods: {
    bindPickerChange(e) {
      this.questionTypeIndex = e.detail.value;
    },
    generateQuiz() {
      // 这是一个模拟的题目生成过程
      if (!this.text.trim()) {
        uni.showToast({
          title: '请输入文本内容',
          icon: 'none'
        });
        return;
      }
      this.generatedQuestions = [];
      for (let i = 0; i < this.questionCount; i++) {
        this.generatedQuestions.push({
          question: `这是根据文本生成的第${i + 1}个问题？`,
          choices: ["选项A", "选项B", "选项C", "选项D"],
          answer: "A"
        });
      }
    }
  }
};
</script>

<style scoped>
.quiz-generator-container {
  background-color: #f5f6fa;
  min-height: 100vh;
}
.page-content {
  padding: 30rpx;
}
.textarea-container {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.text-input {
  width: 100%;
  font-size: 30rpx;
  min-height: 300rpx;
}
.options-container {
  margin-top: 30rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.option:last-child {
  border-bottom: none;
}
.option-label {
  font-size: 30rpx;
  color: #333;
}
.option-input {
  text-align: right;
  width: 100rpx;
  font-size: 30rpx;
}
.picker {
  font-size: 30rpx;
  color: #303133;
}
.generate-button {
  margin-top: 40rpx;
  background-color: #2c3e50;
  color: #ffffff;
  border-radius: 50rpx;
  font-size: 32rpx;
  height: 90rpx;
  line-height: 90rpx;
}
.results-container {
  margin-top: 40rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.results-title {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #2c3e50;
}
.question-list {
  margin-top: 20rpx;
}
.question-item {
  margin-bottom: 30rpx;
}
.question-text {
  font-size: 30rpx;
  color: #333;
}
.choices {
  margin-top: 15rpx;
  font-size: 28rpx;
  color: #666;
}
.choice {
  display: block;
  margin-bottom: 10rpx;
}
.answer {
  margin-top: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #27ae60;
}
</style>
