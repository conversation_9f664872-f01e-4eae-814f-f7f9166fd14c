<template>
  <view class="essay-correction-container">
    <loading-mask :visible="isLoading" text="请稍候..."></loading-mask>

    <!-- 步骤指示器  -->
    <Stepper :active-step="1"/>

    <!-- 主体内容区 -->
    <view class="content">
      <!-- 选择班级 -->
      <view v-if="classList.length > 0">
        <ClassSelector
          :class-list="classList"
          :class-index="classIndex"
          @class-change="handleClassChange"
        />
      </view>
      <view v-else class="no-class-tip">
        <view class="tip-text">创建班级可以将批改结果和班级、学生关联起来</view>
        <view class="tip-sub-text">您也可以先体验批改功能，稍后再创建班级</view>
        <button class="create-class-btn" @click="goToCreateClass">创建班级</button>
      </view>

      <!-- 作文题目 -->
      <view class="form-section">
        <view class="form-item">
          <text class="form-label">作文题目<text class="required">*</text></text>
          <view class="form-input-box">
            <textarea
              class="essay-title-input"
              v-model="formData.essayTitle"
              placeholder="请输入作文题目"
              :maxlength="1000"
              auto-height
            />
          </view>
        </view>
      </view>

      <!-- 批改标准 -->
      <view class="form-section">
        <view class="form-item">
          <view class="form-label-row">
            <text class="form-label">批改标准<text class="required">*</text></text>
            <text class="manage-btn" @click="goToManageStandards">批改标准管理</text>
          </view>
          <view class="form-input-box">
            <picker
              v-if="standardList.length > 0"
              :value="standardIndex"
              :range="standardDisplayList"
              @change="handleStandardChange"
            >
              <view class="form-input">
                {{ standardDisplayList[standardIndex] || '请选择批改标准' }}
                <uni-icons type="arrowdown" size="16" color="#999"></uni-icons>
              </view>
            </picker>
            <view v-else class="no-standard-tip">
              <view class="tip-text">您还没有创建批改标准，请先创建批改标准</view>
              <button class="create-standard-btn" @click="goToCreateStandard">创建批改标准</button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮组件 -->
    <FooterActions :disabled="!isFormValid" @next="goToNextStep"/>

    <!-- 创建任务确认 modal -->
    <CreateTaskModal
      v-model:visible="showCreateModal"
      :class-name="currentClassName"
      :default-title="createTaskData.title"
      @confirm="handleCreateConfirm"
      @cancel="showCreateModal = false"
    />
  </view>
</template>

<script setup>
import {ref, computed, reactive} from 'vue';
import Stepper from '@/components/Stepper.vue';
import FooterActions from './components/FooterActions.vue';
import CreateTaskModal from './components/CreateTaskModal.vue';
import ClassSelector from './components/ClassSelector.vue';
import {getClassList} from '@/api/class';
import {createTask} from '@/api/task';

import LoadingMask from '@/components/LoadingMask.vue';
import {getCurrentTeacher} from "@/api/teacher";
import {onShow} from "@dcloudio/uni-app";
import {getRubricList} from '@/api/rubric';
import {createEssayTaskConfig} from '@/utils/essayCorrection';

// 状态管理

// 班级列表
const isLoading = ref(true);
const classList = ref([]);
const classIndex = ref(0);
const classData = ref([]); // 存储完整的班级数据
const showCreateModal = ref(false); // 创建任务确认modal显示控制

// 表单数据
const formData = reactive({
  essayTitle: '', // 作文题目
});

// 批改标准
const standardList = ref([]); // 存储完整的标准数据
const standardDisplayList = ref([]); // 存储显示用的标准名称
const standardIndex = ref(-1); // 默认为-1表示未选择

// 计算当前选中的班级名称
const currentClassName = computed(() => {
  return classData.value?.[classIndex.value]?.className || '';
});

// 表单验证
const isFormValid = computed(() => {
  return formData.essayTitle.trim() !== '' &&
         standardList.value.length > 0 &&
         standardIndex.value >= 0;
});

// 任务创建数据
const createTaskData = reactive({
  title: '',
  teacherId: '',
  classId: 0,
  correctionImage: '',
  correctionType: '',
  correctionParsedText: '', // 解析的文本内容
  rubricId: 0, // 批改模版ID
  taskType: 3 // 作文批改类型
});




// 获取批改标准列表
const fetchStandardList = async () => {
  try {
    const res = await getRubricList({pageSize: 100, pageNo: 1});
    if (res.code === 0 && res.data && res.data.list) {
      // 保存完整的标准数据
      standardList.value = res.data.list;
      // 提取标准名称作为显示列表
      standardDisplayList.value = res.data.list.map(item => `${item.title}-${item.totalScore}分`);

      // 默认选中第一个标准
      if (standardList.value.length > 0) {
        standardIndex.value = 0;
      }
    }
  } catch (error) {
    console.error('获取批改标准列表失败', error);
    uni.showToast({
      title: '获取批改标准列表失败',
      icon: 'none'
    });
  }
};

// 获取班级列表
const fetchClassList = async () => {
  try {
    const res = await getClassList({pageSize: 100, pageNo: 1});
    if (res.code === 0 && res.data && res.data.list) {
      // 保存完整的班级数据
      classData.value = res.data.list;
      // 提取班级名称作为显示列表
      classList.value = res.data.list.map(item => item.className);

      if (createTaskData.classId) {
        return
      }

      // 默认选中第一个班级
      if (classList.value.length > 0) {
        classIndex.value = 0;
        const defaultClass = classData.value[0];
        createTaskData.classId = defaultClass.id;

      }
    }
  } catch (error) {
    console.error('获取班级列表失败', error);
    uni.showToast({
      title: '获取班级列表失败',
      icon: 'none'
    })
  } finally {
    isLoading.value = false;
  }
};





// 班级选择
const handleClassChange = (value) => {
  classIndex.value = value;
  // 更新createTaskData中的classId
  if (classData.value[value]) {
    const selectedClass = classData.value[value];
    createTaskData.classId = selectedClass.id;
  }
};

// 批改标准选择
const handleStandardChange = (e) => {
  standardIndex.value = e.detail.value;
};

// 前往创建班级页面
const goToCreateClass = () => {
  uni.navigateTo({
    url: '/pages/class-manage/index'
  });
};

// 前往管理批改标准页面
const goToManageStandards = () => {
  uni.navigateTo({
    url: '/pages/standard-manage/index'
  });
};

// 前往创建批改标准页面
const goToCreateStandard = () => {
  uni.navigateTo({
    url: '/pages/standard-manage/detail?mode=add'
  });
};

// 显示创建批改标准确认框
const showCreateStandardConfirm = () => {
  uni.showModal({
    title: '提示',
    content: '您还没有创建批改标准，是否立即创建？',
    confirmText: '立即创建',
    cancelText: '稍后再说',
    success: (res) => {
      if (res.confirm) {
        goToCreateStandard();
      }
    }
  });
};

// 生成任务标题
const generateTaskTitle = (className) => {
  const now = new Date();
  const dateStr = `${now.getFullYear()}年${now.getMonth() + 1}月${now.getDate()}日`;
  const timeStr = `${now.getHours()}:${String(now.getMinutes()).padStart(2, '0')}`;
  return `${className} 作文 ${dateStr} ${timeStr}`;
};

// 前往下一步
const goToNextStep = async () => {
  // 确保填写了作文题目
  if (!formData.essayTitle.trim()) {
    uni.showToast({
      title: '请填写作文题目',
      icon: 'none'
    });
    return;
  }

  // 确保有批改标准
  if (standardList.value.length === 0) {
    showCreateStandardConfirm();
    return;
  }

  // 确保选择了批改标准
  if (standardIndex.value < 0) {
    uni.showToast({
      title: '请选择批改标准',
      icon: 'none'
    });
    return;
  }

  try {
    // 使用工具函数创建任务配置
    const taskConfig = createEssayTaskConfig({
      title: formData.essayTitle,
      teacherId: createTaskData.teacherId,
      classId: createTaskData.classId,
      rubricId: standardList.value[standardIndex.value].id,
      essayTitle: formData.essayTitle
    });

    // 更新任务数据
    Object.assign(createTaskData, taskConfig);

    // 生成默认任务标题
    if (classList.value.length > 0) {
      const selectedClass = classData.value[classIndex.value];
      createTaskData.title = generateTaskTitle(selectedClass.className);
    } else {
      // 没有班级时生成默认标题
      const now = new Date();
      const dateStr = `${now.getFullYear()}年${now.getMonth() + 1}月${now.getDate()}日`;
      const timeStr = `${now.getHours()}:${String(now.getMinutes()).padStart(2, '0')}`;
      createTaskData.title = `作文批改任务 ${dateStr} ${timeStr}`;
      createTaskData.classId = 0; // 设置为0表示没有班级
    }

    // 显示创建确认 modal
    showCreateModal.value = true;
  } catch (error) {
    console.error('创建任务配置失败', error);
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    });
  }
};

// 处理任务创建确认
const handleCreateConfirm = async (taskTitle) => {
  try {
    uni.showLoading({title: '正在创建任务...'});

    // 获取教师信息
    if (!uni.getStorageSync('userInfo')) {
      await getCurrentTeacher()
    }
    createTaskData.teacherId = uni.getStorageSync('userInfo').id;
    createTaskData.title = taskTitle;

    // 调用创建任务接口
    const res = await createTask(createTaskData);
    uni.hideLoading();

    // 显示创建成功提示
    uni.showToast({
      title: '创建成功',
      icon: 'success',
      mask: true,
      duration: 1500
    });

    // 延迟1.5秒后自动跳转到下一步
    setTimeout(() => {
      uni.navigateTo({
        url: `/pages/essay-correction/step-3/step-3?taskId=${res.data}`
      });
    }, 1000);
  } catch (error) {
    uni.hideLoading();
    console.error('创建任务失败', error);
    uni.showToast({
      title: '创建任务失败',
      icon: 'none'
    });
  }
};



// 页面加载时获取数据
onShow(() => {
  fetchClassList();
  fetchStandardList();
});


</script>

<style lang="scss" scoped>
.essay-correction-container {
  padding-top: 88rpx; // 为固定的步骤指示器留出空间
}

// 主体内容区
.content {
  flex: 1;
  padding: 32rpx;
  margin-top: 60rpx; // 为步骤指示器留出空间
  padding-bottom: 200rpx; // 为固定的footer留出空间
}

// 表单区域样式
.form-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  overflow: visible;

  .form-item {
    .form-label-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .form-label {
        margin-bottom: 0;
      }
    }

    .form-label {
      font-size: 30rpx;
      color: #333;
      font-weight: 600;
      margin-bottom: 20rpx;
      display: block;
      line-height: 1.4;

      .required {
        color: #ff4d4f;
        margin-left: 4rpx;
      }
    }

    .manage-btn {
      font-size: 26rpx;
      color: #007aff;
      padding: 8rpx 16rpx;
      border: 2rpx solid #007aff;
      border-radius: 20rpx;
      background-color: transparent;
      white-space: nowrap;
      flex-shrink: 0;
      box-sizing: border-box;
      line-height: 1.4;
      display: inline-block;

      &:active {
        background-color: #f0f8ff;
      }
    }

    .form-input-box {
      background-color: #f8f9fa;
      border-radius: 12rpx;
      border: 2rpx solid #e9ecef;
      transition: border-color 0.3s ease;

      &:focus-within {
        border-color: #007aff;
        background-color: #fff;
      }

      .form-input {
        height: 72rpx;
        padding: 0 20rpx;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 28rpx;
        color: #333;

        &::placeholder {
          color: #999;
        }
      }

      .essay-title-input {
        width: 100%;
        min-height: 200rpx;
        padding: 24rpx;
        font-size: 30rpx;
        color: #333;
        background-color: transparent;
        border: none;
        outline: none;
        resize: none;
        line-height: 1.6;

        &::placeholder {
          color: #999;
        }
      }
    }


  }
}

// 没有班级时的提示样式
.no-class-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx 0;

  .tip-text {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 12rpx;
  }

  .tip-sub-text {
    font-size: 24rpx;
    color: #666;
    margin-bottom: 20rpx;
  }

  .create-class-btn {
    width: 240rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background-color: #007aff;
    color: #fff;
    font-size: 28rpx;
    border-radius: 40rpx;
  }
}

// 没有批改标准时的提示样式
.no-standard-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  margin: 20rpx 0;

  .tip-text {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 20rpx;
    text-align: center;
  }

  .create-standard-btn {
    width: 280rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background-color: #007aff;
    color: #fff;
    font-size: 28rpx;
    border-radius: 40rpx;
  }
}


</style>
