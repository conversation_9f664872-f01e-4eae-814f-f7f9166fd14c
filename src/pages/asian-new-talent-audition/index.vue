<template>
  <view class="audition-page">
    <view class="page-header">
      <image class="header-bg" src="./image/bg-top.png" mode="aspectFill"></image>
      <image class="header-decoration" src="./image/ellipse-decoration.png" mode="aspectFill"></image>
      <image class="header-figure" src="./image/main-figure.png" mode="aspectFill"></image>
      <view class="header-nav">
        <view class="nav-back">
          <image class="back-arrow" src="./image/back-arrow.png"></image>
        </view>
        <view class="nav-title">
          <image class="logo-bg" src="./image/logo-bg.png"></image>
          <image class="logo-icon" src="./image/logo-icon.png"></image>
          <text>创建选手参赛信息</text>
        </view>
      </view>
      <view class="header-text">
        <text class="title-line1">闪耀舞台，为你而开</text>
        <text class="title-line2">尊敬的参赛者！</text>
        <text class="title-line3">欢迎报名2025亚洲新人海选</text>
      </view>
      <view class="progress-bar">
        <image class="progress-bg" src="./image/progress-bar-bg.png"></image>
        <view class="progress-steps">
            <view class="step">
                <image class="step-icon" src="./image/progress-step-1.png"></image>
                <text class="step-text active">填写信息</text>
            </view>
            <view class="step">
                <image class="step-icon" src="./image/progress-step-2.png"></image>
                <text class="step-text">NO2</text>
            </view>
            <view class="step">
                <image class="step-icon" src="./image/progress-step-3.png"></image>
                <text class="step-text">完成领取奖励</text>
            </view>
        </view>
        <view class="progress-track">
            <view class="progress-fill"></view>
        </view>
        <view class="progress-info">
            <text>请仔细填写以下资料，信息将用于：参赛资格审核；赛事流程通知；获奖证书制作</text>
        </view>
      </view>
    </view>

    <view class="form-container">
        <view class="form-section">
          <text class="section-title">Step1.关于你的基础信息</text>
          <view class="form-group">
            <view class="form-item highlighted">
              <text class="item-label">姓       名<text class="required">*</text>：</text>
              <text class="item-input">请输入你的名称</text>
            </view>
            <view class="form-item">
              <text class="item-label">性       别<text class="required">*</text>：</text>
              <view class="item-input">
                <text>请选择你的性别</text>
                <image class="arrow-icon" src="./image/path_715_385.png"></image>
              </view>
            </view>
            <view class="form-item">
              <text class="item-label">出生日期<text class="required">*</text>：</text>
              <text class="item-input">日期选择器，自动计算年龄</text>
            </view>
          </view>
          <view class="form-item">
            <text class="item-label">昵       称*：</text>
            <text class="item-input">（生成专属参赛ID，如MU-{昵称}）</text>
          </view>
          <view class="form-group">
            <view class="form-item">
              <text class="item-label">手机号<text class="required">*</text>：</text>
              <text class="item-input">（绑定微信自动读取+手动修改）捆绑唯一参赛ID</text>
            </view>
            <view class="form-item">
              <text class="item-label">微信号<text class="required">*</text>：</text>
              <text class="item-input">（系统自动抓取，不可修改）</text>
            </view>
            <view class="form-item">
              <text class="item-label">抖音账号：</text>
              <text class="item-input">（选填，用于宣传素材抓取）</text>
            </view>
          </view>
          <view class="form-group">
            <view class="form-item">
              <text class="item-label">所在地*：</text>
              <text class="item-input">（省+市、用于赛区匹配）</text>
            </view>
            <view class="form-item">
              <text class="item-label">参赛地区选择：</text>
              <text class="item-input">（单选：华东/华南/华北）</text>
            </view>
          </view>
          <view class="form-item">
            <text class="item-label">参赛类型*：</text>
            <text class="item-input">（单选：个人/组合）</text>
          </view>
          <view class="form-group">
            <view class="form-item">
              <text class="item-label">自我标签*：</text>
              <text class="item-input">（3-5个关键词，如：高音王者/原创鬼才）</text>
            </view>
            <view class="form-item">
              <text class="item-label">一句话参赛感言*：</text>
              <text class="item-input">（30字内，用于选手海报展示）</text>
            </view>
          </view>
        </view>

        <view class="form-section">
          <text class="section-title">Step2.关于你的音乐事业</text>
          <view class="form-group">
            <view class="form-item">
              <text class="item-label">音乐制作及舞台表演方面的专业能力<text class="required">*</text>：</text>
              <text class="item-input">（多选：演唱/舞蹈/器乐/创作/其它）</text>
            </view>
            <view class="form-item">
              <text class="item-label">目前是否有MCN公司*：</text>
              <text class="item-input">（是/否 MCN公司名称）</text>
            </view>
            <view class="form-item">
              <text class="item-label">过往演出经历*：</text>
              <text class="item-input">（选填、时间+活动名称+角色）</text>
            </view>
            <view class="form-item">
              <text class="item-label">目前是否有经纪公司或代理公司(含作品)：</text>
              <text class="item-input">（选填、有分约情况请注明）</text>
            </view>
            <view class="form-item">
              <text class="item-label">你更喜欢哪种音乐类型?：</text>
              <text class="item-input">（多选：流行/国风/电子/嘻哈/摇滚等）</text>
            </view>
            <view class="form-item">
              <text class="item-label">你心中的标杆歌手是谁?：</text>
              <text class="item-input">（选填、填写）</text>
            </view>
            <view class="form-item">
              <text class="item-label">最能代表你嗓音特质和唱功的歌曲是?：</text>
              <text class="item-input">（选填、歌曲名称）</text>
            </view>
          </view>
        </view>

        <text class="remark">备注：请如实填写相关内容，参赛中如发现信息不符，大赛组委会有权取消你的参赛资格。</text>

        <view class="action-buttons">
          <view class="btn draft">存草稿</view>
          <view class="btn submit">下一步</view>
        </view>
    </view>

  </view>
</template>

<script>
export default {
  data() {
    return {

    }
  }
}
</script>

<style lang="scss" scoped>
.audition-page {
  width: 100%;
  height: 100%;
  background-color: #fff;
}

.page-header {
  position: relative;
  width: 430px;
  height: 331px;
  .header-bg{
    width: 430px;
    height: 238px;
    position: absolute;
    left: 0px;
    top: 93px;
  }
  .header-decoration{
    width: 249px;
    height: 243px;
    position: absolute;
    left: 221px;
    top: 74px;
  }
  .header-figure{
    width: 202px;
    height: 270px;
    position: absolute;
    left: 266px;
    top: 36px;
  }
  .header-text{
    position: absolute;
    left: 30px;
    top: 112px;
    color: #0A1D44;
    .title-line1{
        font-size: 16px;
    }
    .title-line2, .title-line3{
        font-size: 18px;
        font-weight: 700;
    }
  }

  .header-nav {
    position: absolute;
    top: 38px;
    left: 20px;
    display: flex;
    align-items: center;
    gap: 4px;
    .nav-back{
        width: 10px;
        height: 16px;
    }
    .nav-title{
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;
        font-weight: 700;
        color: #161723;
        .logo-bg, .logo-icon{
            width: 31px;
            height: 31px;
        }
    }
  }

  .progress-bar {
    width: 370px;
    height: 60px;
    position: absolute;
    left: 30px;
    top: 209px;
    .progress-bg{
        width: 344px;
        height: 29px;
        position: absolute;
        left: 13px;
        top: 0;
    }
    .progress-steps{
        display: flex;
        justify-content: space-between;
        width: 240px;
        position: absolute;
        left: 60px;
        top: 27px;
        .step{
            display: flex;
            flex-direction: column;
            align-items: center;
            .step-icon{
                width: 14px;
                height: 14px;
            }
            .step-text{
                font-size: 8px;
                font-weight: 700;
                color: #161723;
                &.active{
                    color: #161723;
                }
            }
        }
    }
    .progress-track{
        width: 272px;
        height: 6px;
        border-radius: 20px;
        background-color: #161723;
        position: absolute;
        left: 15px;
        top: 31px;
        .progress-fill{
            width: 44px;
            height: 4px;
            border-radius: 20px 0 0 20px;
            background-color: #01FF83;
            position: absolute;
            left: 1px;
            top: 1px;
        }
    }
    .progress-info{
        font-size: 10px;
        color: #000;
        letter-spacing: -1px;
        position: absolute;
        left: 22px;
        top: 4px;
    }
  }
}

.form-container {
  padding: 18px 16px 0;

  .form-section {
    margin-bottom: 18px;

    .section-title {
      font-size: 14px;
      color: #8f8f8f;
      margin-bottom: 8px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .form-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 398px;
        height: 56px;
        padding: 0 16px;
        border-radius: 6px;
        background-color: #f7f7f7;
        font-size: 14px;

        &.highlighted {
          border: 1px solid #00c06a;
        }

        .item-label {
          color: #161723;

          .required {
            color: #333;
          }
        }

        .item-input {
          color: #c3c3c3;
          text-align: right;

          .arrow-icon {
            width: 4px;
            height: 7px;
            margin-left: 4px;
          }
        }
      }
    }
  }

  .remark {
    font-size: 14px;
    color: #8f8f8f;
    margin-top: 18px;
  }

  .action-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;

    .btn {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 52px;
      border-radius: 30px;
      font-size: 16px;

      &.draft {
        width: 168px;
        background-color: #efefef;
        color: #8f8f8f;
      }

      &.submit {
        width: 208px;
        background-color: #161723;
        color: #fff;
        font-weight: 700;
      }
    }
  }
}
</style>
