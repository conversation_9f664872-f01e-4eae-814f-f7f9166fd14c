<template>
  <view class="converted-design-page">
    <!-- 顶部状态栏 -->
    <view class="status-bar">
      <text class="time-text">19:13</text>
      <view class="status-icons">
        <image class="status-icon" src="/static/images/signal-icon.png" mode="aspectFit" />
        <image class="status-icon" src="/static/images/wifi-icon.png" mode="aspectFit" />
        <image class="status-icon" src="/static/images/battery-icon.png" mode="aspectFit" />
      </view>
    </view>

    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-left">
        <image class="nav-icon" src="/static/images/back-icon.png" mode="aspectFit" @tap="goBack" />
      </view>
      <text class="nav-title">创建选手参赛信息</text>
      <view class="nav-right"></view>
    </view>

    <!-- 主要内容区域 -->
    <scroll-view class="main-content" scroll-y>
      <!-- 头部横幅 -->
      <view class="header-banner">
        <view class="banner-bg">
          <image class="banner-image" src="/static/images/banner-bg.png" mode="aspectFill" />
        </view>
        <view class="banner-content">
          <text class="banner-title">闪耀舞台，为你而开</text>
          <text class="banner-subtitle">尊敬的参赛者！\n欢迎报名2025亚洲新人海选</text>
        </view>
      </view>

      <!-- 步骤指示器 -->
      <view class="step-indicator">
        <view class="step-item active">
          <view class="step-circle">
            <text class="step-number">1</text>
          </view>
          <text class="step-text">填写信息</text>
        </view>
        <view class="step-line"></view>
        <view class="step-item">
          <view class="step-circle">
            <text class="step-number">2</text>
          </view>
          <text class="step-text">完成领取奖励</text>
        </view>
      </view>

      <!-- 表单区域 -->
      <view class="form-container">
        <!-- Step 1: 基础信息 -->
        <view class="form-section">
          <text class="section-title">Step1.关于你的基础信息</text>

          <view class="form-group">
            <text class="form-label">姓名<text class="required">*</text>：</text>
            <input class="form-input" placeholder="请输入你的名称" v-model="formData.name" />
          </view>

          <view class="form-group">
            <text class="form-label">性别<text class="required">*</text>：</text>
            <picker class="form-picker" :value="formData.genderIndex" :range="genderOptions" @change="onGenderChange">
              <view class="picker-content">
                <text class="picker-text">{{ genderOptions[formData.genderIndex] || '请选择你的性别' }}</text>
                <image class="picker-arrow" src="/static/images/arrow-down.png" mode="aspectFit" />
              </view>
            </picker>
          </view>

          <view class="form-group">
            <text class="form-label">出生日期<text class="required">*</text>：</text>
            <picker class="form-picker" mode="date" :value="formData.birthDate" @change="onDateChange">
              <view class="picker-content">
                <text class="picker-text">{{ formData.birthDate || '日期选择器，自动计算年龄' }}</text>
                <image class="picker-arrow" src="/static/images/arrow-down.png" mode="aspectFit" />
              </view>
            </picker>
          </view>

          <view class="form-group">
            <text class="form-label">昵称<text class="required">*</text>：</text>
            <input class="form-input" placeholder="（生成专属参赛ID，如MU-{昵称}）" v-model="formData.nickname" />
          </view>

          <view class="form-group">
            <text class="form-label">手机号<text class="required">*</text>：</text>
            <input class="form-input" placeholder="（绑定微信自动读取+手动修改）捆绑唯一参赛ID" v-model="formData.phone" />
          </view>

          <view class="form-group">
            <text class="form-label">微信号<text class="required">*</text>：</text>
            <input class="form-input" placeholder="（系统自动抓取，不可修改）" v-model="formData.wechat" disabled />
          </view>

          <view class="form-group">
            <text class="form-label">抖音账号：</text>
            <input class="form-input" placeholder="（选填，用于宣传素材抓取）" v-model="formData.douyin" />
          </view>

          <view class="form-group">
            <text class="form-label">所在地<text class="required">*</text>：</text>
            <input class="form-input" placeholder="（省+市、用于赛区匹配）" v-model="formData.location" />
          </view>

          <view class="form-group">
            <text class="form-label">参赛地区选择：</text>
            <picker class="form-picker" :value="formData.regionIndex" :range="regionOptions" @change="onRegionChange">
              <view class="picker-content">
                <text class="picker-text">{{ regionOptions[formData.regionIndex] || '（单选：华东/华南/华北）' }}</text>
                <image class="picker-arrow" src="/static/images/arrow-down.png" mode="aspectFit" />
              </view>
            </picker>
          </view>

          <view class="form-group">
            <text class="form-label">参赛类型<text class="required">*</text>：</text>
            <picker class="form-picker" :value="formData.typeIndex" :range="typeOptions" @change="onTypeChange">
              <view class="picker-content">
                <text class="picker-text">{{ typeOptions[formData.typeIndex] || '（单选：个人/组合）' }}</text>
                <image class="picker-arrow" src="/static/images/arrow-down.png" mode="aspectFit" />
              </view>
            </picker>
          </view>

          <view class="form-group">
            <text class="form-label">自我标签<text class="required">*</text>：</text>
            <input class="form-input" placeholder="（3-5个关键词，如：高音王者/原创鬼才）" v-model="formData.tags" />
          </view>

          <view class="form-group">
            <text class="form-label">一句话参赛感言<text class="required">*</text>：</text>
            <textarea class="form-textarea" placeholder="（30字内，用于选手海报展示）" v-model="formData.slogan" maxlength="30"></textarea>
          </view>
        </view>

        <!-- Step 2: 音乐事业 -->
        <view class="form-section">
          <text class="section-title">Step2.关于你的音乐事业</text>

          <view class="form-group">
            <text class="form-label">音乐制作及舞台表演方面的专业能力<text class="required">*</text>：</text>
            <input class="form-input" placeholder="（多选：演唱/舞蹈/器乐/创作/其它）" v-model="formData.abilities" />
          </view>

          <view class="form-group">
            <text class="form-label">目前是否有MCN公司<text class="required">*</text>：</text>
            <input class="form-input" placeholder="（是/否 MCN公司名称）" v-model="formData.mcn" />
          </view>

          <view class="form-group">
            <text class="form-label">过往演出经历<text class="required">*</text>：</text>
            <textarea class="form-textarea" placeholder="（选填、时间+活动名称+角色）" v-model="formData.experience"></textarea>
          </view>

          <view class="form-group">
            <text class="form-label">目前是否有经纪公司或代理公司(含作品)：</text>
            <textarea class="form-textarea" placeholder="（选填、有分约情况请注明）" v-model="formData.agency"></textarea>
          </view>

          <view class="form-group">
            <text class="form-label">你更喜欢哪种音乐类型?：</text>
            <input class="form-input" placeholder="（多选：流行/国风/电子/嘻哈/摇滚等）" v-model="formData.musicType" />
          </view>

          <view class="form-group">
            <text class="form-label">你心中的标杆歌手是谁?：</text>
            <input class="form-input" placeholder="（选填、填写）" v-model="formData.idol" />
          </view>

          <view class="form-group">
            <text class="form-label">最能代表你嗓音特质和唱功的歌曲是?：</text>
            <input class="form-input" placeholder="（选填、歌曲名称）" v-model="formData.representativeSong" />
          </view>
        </view>
      </view>

      <!-- 提示信息 -->
      <view class="notice-section">
        <view class="notice-box">
          <image class="notice-icon" src="/static/images/info-icon.png" mode="aspectFit" />
          <text class="notice-text">请仔细填写以下资料，信息将用于：参赛资格审核；赛事流程通知；获奖证书制作</text>
        </view>
        <text class="disclaimer">备注：请如实填写相关内容，参赛中如发现信息不符，大赛组委会有权取消你的参赛资格。</text>
      </view>
    </scroll-view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <button class="action-btn draft-btn" @tap="saveDraft">存草稿</button>
      <button class="action-btn next-btn" @tap="nextStep">下一步</button>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ConvertedDesign',
  data() {
    return {
      // 表单数据
      formData: {
        name: '',
        genderIndex: -1,
        birthDate: '',
        nickname: '',
        phone: '',
        wechat: '',
        douyin: '',
        location: '',
        regionIndex: -1,
        typeIndex: -1,
        tags: '',
        slogan: '',
        abilities: '',
        mcn: '',
        experience: '',
        agency: '',
        musicType: '',
        idol: '',
        representativeSong: ''
      },
      // 选择器选项
      genderOptions: ['男', '女'],
      regionOptions: ['华东', '华南', '华北'],
      typeOptions: ['个人', '组合']
    }
  },
  onLoad() {
    console.log('参赛信息填写页面已加载')
    this.initWechatInfo()
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 初始化微信信息
    initWechatInfo() {
      // 模拟自动获取微信信息
      this.formData.wechat = 'wx_auto_generated'
    },

    // 性别选择
    onGenderChange(e) {
      this.formData.genderIndex = e.detail.value
    },

    // 日期选择
    onDateChange(e) {
      this.formData.birthDate = e.detail.value
    },

    // 地区选择
    onRegionChange(e) {
      this.formData.regionIndex = e.detail.value
    },

    // 类型选择
    onTypeChange(e) {
      this.formData.typeIndex = e.detail.value
    },

    // 保存草稿
    saveDraft() {
      uni.showToast({
        title: '草稿已保存',
        icon: 'success'
      })
      // 这里可以添加保存到本地存储的逻辑
      uni.setStorageSync('registration_draft', this.formData)
    },

    // 下一步
    nextStep() {
      if (this.validateForm()) {
        uni.showToast({
          title: '信息提交成功',
          icon: 'success'
        })
        // 这里可以添加跳转到下一步的逻辑
        // uni.navigateTo({ url: '/pages/registration/step2' })
      }
    },

    // 表单验证
    validateForm() {
      const required = ['name', 'nickname', 'phone', 'location', 'tags', 'slogan', 'abilities', 'mcn']
      for (let field of required) {
        if (!this.formData[field]) {
          uni.showToast({
            title: `请填写${this.getFieldName(field)}`,
            icon: 'none'
          })
          return false
        }
      }

      if (this.formData.genderIndex === -1) {
        uni.showToast({
          title: '请选择性别',
          icon: 'none'
        })
        return false
      }

      if (!this.formData.birthDate) {
        uni.showToast({
          title: '请选择出生日期',
          icon: 'none'
        })
        return false
      }

      return true
    },

    // 获取字段中文名
    getFieldName(field) {
      const fieldNames = {
        name: '姓名',
        nickname: '昵称',
        phone: '手机号',
        location: '所在地',
        tags: '自我标签',
        slogan: '参赛感言',
        abilities: '专业能力',
        mcn: 'MCN公司信息'
      }
      return fieldNames[field] || field
    }
  }
}
</script>

<style lang="scss" scoped>
.converted-design-page {
  width: 100%;
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 状态栏 */
.status-bar {
  height: 88rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx;
  background-color: #ffffff;
}

.time-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.status-icon {
  width: 48rpx;
  height: 32rpx;
}

/* 导航栏 */
.nav-bar {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e9ecef;
}

.nav-left, .nav-right {
  width: 80rpx;
  display: flex;
  justify-content: center;
}

.nav-icon {
  width: 48rpx;
  height: 48rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #212529;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  background-color: #f8f9fa;
}

/* 头部横幅 */
.header-banner {
  position: relative;
  height: 300rpx;
  margin: 32rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.banner-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, rgba(79, 172, 254, 0.8) 0%, rgba(0, 242, 254, 0.8) 100%);
}

.banner-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 16rpx;
}

.banner-subtitle {
  font-size: 28rpx;
  color: #ffffff;
  text-align: center;
  line-height: 1.5;
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 40rpx 32rpx;
  padding: 32rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.step-circle {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e9ecef;
  color: #6c757d;
}

.step-item.active .step-circle {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: #ffffff;
}

.step-number {
  font-size: 32rpx;
  font-weight: 600;
}

.step-text {
  font-size: 24rpx;
  color: #6c757d;
}

.step-item.active .step-text {
  color: #4facfe;
  font-weight: 600;
}

.step-line {
  width: 120rpx;
  height: 4rpx;
  background-color: #e9ecef;
  margin: 0 32rpx;
}

/* 表单容器 */
.form-container {
  margin: 0 32rpx 32rpx;
}

.form-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #212529;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #e9ecef;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #495057;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.required {
  color: #dc3545;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #495057;
  background-color: #ffffff;
  box-sizing: border-box;
}

.form-input:focus, .form-textarea:focus {
  border-color: #4facfe;
  outline: none;
}

.form-input[disabled] {
  background-color: #f8f9fa;
  color: #6c757d;
}

.form-textarea {
  min-height: 120rpx;
  resize: vertical;
}

.form-picker {
  width: 100%;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  background-color: #ffffff;
}

.picker-text {
  font-size: 28rpx;
  color: #495057;
  flex: 1;
}

.picker-arrow {
  width: 32rpx;
  height: 32rpx;
  margin-left: 16rpx;
}

/* 提示信息区域 */
.notice-section {
  margin: 0 32rpx 32rpx;
}

.notice-box {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  background-color: #e3f2fd;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.notice-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.notice-text {
  flex: 1;
  font-size: 26rpx;
  color: #1976d2;
  line-height: 1.5;
}

.disclaimer {
  font-size: 24rpx;
  color: #6c757d;
  line-height: 1.5;
  text-align: center;
}

/* 底部操作按钮 */
.bottom-actions {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #e9ecef;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.draft-btn {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.next-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: #ffffff;
}

.action-btn:active {
  opacity: 0.8;
}

/* 响应式适配 */
@media screen and (max-width: 600rpx) {
  .header-banner {
    height: 240rpx;
    margin: 24rpx;
  }

  .banner-title {
    font-size: 40rpx;
  }

  .banner-subtitle {
    font-size: 24rpx;
  }

  .form-container {
    margin: 0 24rpx 24rpx;
  }

  .form-section {
    padding: 24rpx;
  }

  .section-title {
    font-size: 32rpx;
  }
}
</style>
